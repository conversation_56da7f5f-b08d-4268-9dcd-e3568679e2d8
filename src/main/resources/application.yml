server:
  # 后端端口
  port: ${PORT:8860}
  tomcat:
    # tomcat的URI编码，保证get请求采用指定的编码，这里是utf-8编码
    uri-encoding: utf-8

sinoyd:
  mail:
    from: <EMAIL>

frame-boot:
  # swagger相关配置
  swagger:
    # 是否启用swagger，true: 启用， false: 不启用
    enabled: true
    # swagger扫描包
    basePackage: com.sinoyd.boot
    # swagger在线项目名称
    title: 云框架
    # swagger在线项目描述
    description: Rest API
    # swagger 版本
    version: 1.0
    # 作者
    authorName: sinoyd
    # 作者邮箱
    authorEmail: <EMAIL>
  # 框架注册码服务校验地址
  regVerifyUrl: ${REG_VERIFY_URL:http://*************:8760/api/register/auth/verify}
  # 权限同步网关服务地址前缀
  gateUrlPrefix: ${GATE_URL_PREFIX:http://localhost:9090/api/proxy}
  # rest请求超时设置(单位：毫秒)
  restRequestTimeout: 3000
  # 接口日志保留天数
  restLog:
    saveDay: ${RESTLOG_SAVE_DAY:30}

  # 用户中心相关配置
  user-center:
    # 是否为本地离线模式(true: 离线模式，false：云模式)
    localMode: ${USER_CENTER_LOCALMODE:true}
    # 用户中心接口服务地址
    serverUrl: ${USER_CENTER_SERVERURL:http://*************:8850/api/auth/users}
    # 客户端ID(云版模式需要配置)
    clientId: ${USER_CENTER_CLIENTID:8}
    # 客户端秘钥(云版模式需要配置)
    clientSecret: ${USER_CENTER_CLIENTSECRET:jIkjGEIfn7lYem52gcnfqHiLGkAEav98DfI5BRt4}
    # 短信内容中系统名称(云版模式需要配置)
    sendSmsProductName: ${USER_CENTER_SMSNAME:云框架}
    #单位有效期过期提醒时间，以天为单位
    remindCycle: 30
    #登录类型 离线模式才起效果
    loginTypes:
      - code: useName #用户名
        name: 用户名 # 描述
      - code: email #邮箱
        name: 邮箱 # 描述

  # 用户登录失败锁相关配置
  user-lock:
    # 用户登录失败是否锁定账号，true: 锁定，false: 不锁定
    enabled: false
    # 连续登录失败周期时长(单位：分钟)
    cycleTime: 30
    # 最大失败次数
    maxFailNum: 3
    # 锁定时长(单位：分钟)
    lockTime: 15

  # 拦截器匹配url配置
  interceptor:
    # 用户token拦截Url(多个用英文,分割不包含空格)
    userIncludePatterns:
    # 服务token拦截Url(多个用英文,分割不包含空格)
    serviceIncludePatterns:
    # 用户、服务token排除拦截Url(多个用英文,分割不包含空格)
    excludePathPatterns:

api:
  # rest接口添加统一前缀
  prefix: /api

# spring 相关配置
spring:
  banner:
    location: classpath:banner.txt
  autoconfigure:
    # 排除自动加载的类
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  profiles:
    # 加载业务系统需要覆盖的配置项，dev是指application-dev.yml文件
    include: ${PROCOVER:dev}
  application:
    #  应用名称
    name: sinoyd-frame
  thymeleaf:
    prefix: classpath:/template/
    suffix: .html
    content-type: text/html
    cache: false
    encoding: UTF-8
    mode: LEGACYHTML5

  jackson:
      # Jackson 返回时间时的格式
      date-format: yyyy-MM-dd HH:mm:ss
      # Jackson 返回时间的时区
      time-zone: GMT+8
  cache:
    # 指定缓存管理器，LIMS使用redis
    type: redis
  redis:
    # Redis主机
    host: ${REDIS_HOST:**************}
    # Redis 密码，如果没有，直接空
    password: ${REDIS_PWD:sinoyd}
    # 连接超时时间（毫秒）
    timeout: 10000
    # Redis 库
    database: ${REDIS_DB:3}
    pool:
      # 连接池最大连接数（使用负值表示没有限制） 默认 8
      max-active: 8
      # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
      max-wait: -1
      # 连接池中的最大空闲连接 默认 8
      max-idle: 8
      # 连接池中的最小空闲连接 默认 0
      min-idle: 0

    # 数据源
    datasource:
      # 业务数据源
      primary:
        # DB驱动名称
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
        # DB连接url
        url: ***********************************************************************
        # DB用户名
        username: sa
        # DB密码
        password: qwe123!@#
        # 指定启动连接池时，初始建立的连接数量
        initialSize: 10
        # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
        minIdle: 5
        # 指定连接池中最大的活跃连接数
        maxActive: 60
        # 指定连接池等待连接返回的最大等待时间，单位毫秒
        maxWait: 60000
        # 当连接空闲时，是否执行连接测试
        testWhileIdle: true
        # 指定是否池化statements
        poolPreparedStatements: false

      # 框架数据源
      frame:
        # DB驱动名称
        driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
        # DB连接url
        url: jdbc:sqlserver://${SQLSER_HOST:*************\MYSQLSERVER2012};DatabaseName=frameCloudBoot5.1
        # DB用户名
        username: sa
        # DB密码
        password: qwe123!@#
        # 指定启动连接池时，初始建立的连接数量
        initialSize: 10
        # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
        minIdle: 5
        # 指定连接池中最大的活跃连接数
        maxActive: 60
        # 指定连接池等待连接返回的最大等待时间，单位毫秒
        maxWait: 60000
        # 当连接空闲时，是否执行连接测试
        testWhileIdle: true
        # 指定是否池化statements
        poolPreparedStatements: false

      # 工作流数据源
      activiti:
        # DB驱动名称
        driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
        # DB连接url
        url: ***********************************************************************
        # DB用户名
        username: sa
        # DB密码
        password: qwe123!@#
        type: com.alibaba.druid.pool.DruidDataSource
        # 指定启动连接池时，初始建立的连接数量
        initialSize: 5
        # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
        minIdle: 5
        # 指定连接池中最大的活跃连接数
        maxActive: 20
        # 指定连接池等待连接返回的最大等待时间，单位毫秒
        maxWait: 60000
        # 指定空闲连接检查、废弃连接清理、空闲连接池大小调整之间的操作时间间隔，单位毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 指定一个空闲连接最少空闲多久后可被清除，单位毫秒
        minEvictableIdleTimeMillis: 300000
        # 指定获取连接时连接校验的sql查询语句
        validationQuery: SELECT 1
        # 当连接空闲时，是否执行连接测试
        testWhileIdle: true
        # 当从连接池借用连接时，是否测试该连接，true：测试，false：不测试
        testOnBorrow: false
        # 在连接归还到连接池时是否测试该连接，true：测试，false：不测试
        testOnReturn: false
        # 指定是否池化statements
        poolPreparedStatements: false
        # 指定PSCache的大小
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters， wall指防火墙
        filters: stat,wall,log4j
        # 通过connectProperties属性来打开mergeSql功能
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        # 合并多个DruidDataSource的监控数据
        useGlobalDataSourceStat: true

      # 仪器解析数据源
      instrumentParse:
        # DB驱动名称
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
        # DB连接url
        url: ***************************************************************************
        # DB用户名
        username: sa
        # DB密码
        password: qwe123!@#
        # 指定启动连接池时，初始建立的连接数量
        initialSize: 10
        # 指定必须保持连接的最小值(For DBCP and Tomcat connection pools)
        minIdle: 5
        # 指定连接池中最大的活跃连接数
        maxActive: 50
        # 指定连接池等待连接返回的最大等待时间，单位毫秒
        maxWait: 60000
        # 当连接空闲时，是否执行连接测试
        testWhileIdle: true

    # JPA相关配置
    jpa:
      # DB类型
      database: sql_server
      # 方言配置
      database-platform: com.sinoyd.frame.configuration.CustomSQLServer2008JSONDialect
      hibernate:
        # 自动创建|更新|验证数据库表结构，一般开发时用
        # ddl-auto: update
        naming:
          # 设置使用的命名策略，负责模型对象层次的处理，将对象模型处理为逻辑名称，
          # 当没有使用@Table和@Column注解时，implicit-strategy配置项才会被使用，当对象模型中已经指定时，implicit-strategy并不会起作用
          implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
          # 负责映射成真实的数据名称的处理，将逻辑名称处理为物理名称，一定会被应用，与对象模型中是否显式地指定列名或者已经被隐式决定无关
          physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      # 在日志中显示sql， true：显示，false：不显示。一般开发是配置true
      show-sql: true
      properties:
        hibernate:
          session_factory:
            # JPA拦截器
            statement_inspector: com.sinoyd.frame.inspector.JpaInterceptor
          # JPA打印sql的时候，true：格式化，false：不格式化
          format_sql: true

#  mq配置
  rabbitmq:
    host: ${MQ_HOST:************}
    port: ${MQ_PORT:5672}
    username: ${MQ_USERNAME:lims}
    password: ${MQ_PWD:d5UH9c5OJPSz3mtj}
    listener:
      direct:
        acknowledge-mode: manual
      simple:
        acknowledge-mode: manual

# mybatis 相关配置
mybatis-plus:
  # 是否打开SQL执行效率插件【生产环境建议关闭】打开会影响性能
  openSqlPerfStat: false
  # mapper xml文件路径
  mapper-locations: classpath:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sinoyd.boot.frame.sys.model
  # 枚举扫描路径
  typeEnumsPackage: com.sinoyd.boot.common.model.enums
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 2
    #字段策略 0:"忽略判断",1:"非 NULL判断",2:"非空判断"
    field-strategy: 1
    #驼峰下划线转换
    db-column-underline: false
    #刷新mapper 调试神器
    refresh-mapper: true
    #逻辑删除配置，1为删除
    logic-delete-value: 1
    #逻辑删除配置，0为不删除
    logic-not-delete-value: 0
  configuration:
    # 是否将带有下划线的表字段映射为驼峰格式的实体类属性
    map-underscore-to-camel-case: false
    # 是否使用二级缓存
    cache-enabled: false

# 文件相关配置
fileProps:
  # 文件上传路径
  filePath: E:/LIMS5.1/source/lims-proxy/files
  # 临时目录（生成的报表会先放临时目录）
  outputPath: E:/LIMS5.1/source/lims-proxy/outputs
  # 仪器解析文件路径（从仪器解析项目获取，LIMS需要读取相关文件）
  instrumentParseFilePath: E:/LIMS5.1/source/lims-proxy/files/instruments
  # 报表、采样单、原始记录单等模板
  templatePath: E:/LIMS5.1/source/lims-proxy/files/report_templates
  # 允许上传的文件类型
  fileSuffix: jpg,doc,docx,xls,xlsx,jpeg,png,txt,mp3,flac,avi,mp4,apk,pdf,xml

# 日志相关配置
logging:
  # 日志等级(debug、info、warn、error)
  level: info
  # 日志存放路径
  file: d:/SpringBoot/SinoydLIMS51.log

#value
projectTask:
  projectModules:
   - moduleName : 项目登记 # 模块名称
     moduleCode : projectRegister # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 例行登记 # 模块名称
     moduleCode: routineRegister # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 多企业污染源 # 模块名称
     moduleCode: pollutionEnterprises # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 项目审核 # 项目审核
     moduleCode : projectAudit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - projectIssue # 项目下达
   - moduleName : 项目下达 # 项目下达
     moduleCode : projectIssue # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 方案登记 # 模块名称
     moduleCode: makeSolution # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 方案审核 # 模块名称
     moduleCode: auditSolution # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 采样准备 # 采样准备
     moduleCode : prepareSample # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 现场委托送样 # 现场委托送样
     moduleCode : localSendSample # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 现场任务 # 现场任务
     moduleCode : localTask # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - localDataCheck # 现场数据复核
      - localDataAudit # 现场数据审核
   - moduleName : 现场数据复核 # 现场数据复核
     moduleCode : localDataCheck # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 现场数据审核 # 现场数据审核
     moduleCode : localDataAudit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品交接 # 样品交接
     moduleCode : sampleReceive # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品分配 # 样品分配
     moduleCode : sampleAssign # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 编制报告 # 编制报告
     moduleCode : reportEdit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 报告审核 # 报告审核
     moduleCode : reportAudit # 模块编码、
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - reportSign # 报告签发
   - moduleName : 报告签发 # 报告签发
     moduleCode : reportSign # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 任务办结 # 任务办结
     moduleCode : projectEnd # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 质控任务登记 # 质控任务登记
     moduleCode : qcRegister # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 数据汇总 # 数据汇总
     moduleCode : qcCollect # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 评价结果 # 评价结果
     moduleCode : qcEvaluate # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 费用管理 # 费用管理
     moduleCode : costInfo # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
     bindingModules:
      - costInfoAudit # 费用审核
      - costInfoApprove # 费用审批
   - moduleName : 费用审核 # 费用审核
     moduleCode : costInfoAudit # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 费用审批 # 费用审批
     moduleCode : costInfoApprove # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 重点项目 # 重点项目 #
     moduleCode : keyProject # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 公告 # 公告
     moduleCode : notice # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 快速导航 # 快速导航
     moduleCode : fastNavigation # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 委托统计 # 委托统计
     moduleCode : customer # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品统计 # 样品统计
     moduleCode : sample # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 检测状态统计 # 检测状态统计
     moduleCode : analyseStatus # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 检测及时率 # 检测及时率
     moduleCode : analysePromptness # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 业务量统计 # 业务量统计
     moduleCode : business # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 样品量统计 # 样品量统计
     moduleCode : sampleNum # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 1 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName : 消息提醒 # 消息提醒
     moduleCode : message # 模块编码
     cacheTimeout: 300 # 缓存失效时间 秒单位计算
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 订单登记 # 模块名称
     moduleCode: orderRegister # 模块编码
     operationCode:   # 权限操作编码
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存
   - moduleName: 订单审核 # 模块名称
     moduleCode: orderAudit  # 模块编码
     operationCode:   # 权限操作编码
     value: 2 # 缓存方式：1:指按权限缓存，2:按人员缓存

# 是否暴露端点，true：暴露，false：不暴露
management:
  security:
    enabled: false

#jwt token相关设置
jwt:
  user:
    # token存放请求头部key名
    token-header: Authorization
    # 有效期（单位：分钟）
    expire: ${JWT_EXPIRE:120}
    delayed: ${JWT_DELAYED:5}
    # 用户加密秘钥
    rsa-secret: xx1WET12^%3^(WE45
  client:
    # 微服务加密秘钥，LIMS暂时无用
    rsa-secret: x2318^^(*WRYQWR(QW&T

# APP下载地址
mobile:
  client:
    download:
      path: http://*************:9098/api/sinoyd-lims/base/document/appDownload

# 远大LIMS基础数据共同库地址
standardData:
  host: http://*************:6001
  loginId: 13345678901
  password: 111111

centerReport:
  url: ${reporturl:http://*************:6001/api/sinoyd-report}

# flyway文件位置配置
flyway:
  path:
    frame: /db/migration/frame # 框架相关sql文件位置
    lims:
      ddl: /db/migration/lims/ddl # lims 相关表操作ddl sql文件位置
      data: /db/migration/lims/data # lims 相关表操作初始化数据sql文件位置
    rcc:
      ddl: /db/migration/rcc/ddl # rcc 相关表操作ddl sql文件位置
      data: /db/migration/rcc/data # rcc 相关表操作初始化数据sql文件位置
    act:
      data: NONE # act 相关表操作初始化数据sql文件位置
    project: NONE # 项目上个性化数据库脚本文件位置

ocr:
#  url: ${OCRURL:http://**************:8866/predict/ocr_system}
#  url: http://*************:19990/api/call-agent/2
  url: ${OCRURL:http://*************:19990/api/call-agent/2}
  prompt: 角色：你是位资深的光学数据识别专家。任务：你需要识别图片内容并将内容按照指定格式输出要求,1.禁止将中文转成英文,禁止将单位转成中文,2.数据中的*需要去掉,3.数据不需要单位,4.采样日期和采样时间不需要分开。指定的json格式示例如下{‘跟踪率’:'23','跟踪率单位':'L'}，去掉不必要的换行符，生成的json文本要能支持直接转换为java中的JSONObject。回答不需要其余说明，只需要输出json文本。

aop:
  des:
    enabled: ${AOP_DES_ENABLED:false} #外部密策加密是否启用
  sign:
    enabled: ${AOP_SIGN_ENABLED:false} #外部延签功能是否启用
    
avoid:
  resubmit:
    enabled: ${AVOID_RESUBMIT_ENABLED:true} #是否启用防止重复提交功能
    excludeUri: ${AVOID_RESUBMIT_EXCLUDE_URI:/api/base/document/upload} #需要排除的接口，多个用英文逗号拼接
    
#仪器接入配置信息
instrument-gather:
  # 在线平台的唯一标示
  appID: ${GATHER_APPID:16}
  # 秘钥
  appSecret: ${GATHER_APPSECRET:9e5bf0c1e2646ead56e239039cd08f7b}
  # 统一社会信用代码
  uscc: ${GATHER_USCC:123456789999999999}
  # 实时热数据URL
  hotdata_url: ${GATHER_HOTDATA_URL:https://ddc.envchina.com/api/pl-hotdata}
  # 查询数据URL
  rtdata_url: ${GATHER_RTDATA_URL:https://ddc.envchina.com/api/pl-rtdata}
  
# 导入时允许填写的时间类型格式
import:
  allow-date-format: ${IMPORT_ALLOW_FORMAT:yyyy-MM-dd,yyyy/MM/dd,yyyyMMdd,yyyy.MM.dd}
  

#环保企业通项目推送配置信息
environment-enterprise:
  # 在线平台的唯一标示
  appID: ${APPID:0}
  # 秘钥
  appSecret: ${APPSECRET:84761749b186d1828ce64c38273ac65b}
  # 推送接口地址
  push_gate_url: ${PUSH_GATE_URL:http://192.168.6.10:9819}
  # 推送接口请求路径
  push_url: ${PUSH_URL:/api/open/sample-detect/push}
  # token验证接口请求路径
  login_url: ${LOGIN_URL:/api/open/sample-detect/login}
  
 #上海环境院推送相关配置
shanghaiAPI:
  url:  ${SH_PUSH_URL:https://www.shemss.cn:10004/WebService1/WebService.asmx}
  username: ${SH_PUSH_USERNAME:hghj}
  password: ${SH_PUSH_PASSWORD:HGHJbh@1980}


#排污许可证数据获取配置信息
pollutant-discharge-permit:
  # 是否启用排污许可证功能
  enabled: ${PDP_ENABLED:false}
  # 自行监测数据请求配置
  monitor-data:
    # 在线平台的唯一标示
    appId: ${PDP_MONITOR_DATA_APPID:16}
    # 秘钥
    appSecret: ${PDP_MONITOR_DATA_APPSECRET:9e5bf0c1e2646ead56e239039cd08f7b}
    # 数据获取请求网关地址
    gate-url: ${PDP_MONITOR_DATA_GATE_URL:https://ddc.envchina.com}
    # 数据获取请求接口
    url: ${PDP_MONITOR_DATA_URL:/api/pollic}