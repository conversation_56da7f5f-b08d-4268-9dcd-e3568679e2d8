package com.sinoyd.lims.api.service.offline.strategy.config;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineConfigPackageVO;
import org.springframework.stereotype.Component;

/**
 * 检测类型配置数据转换策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/24
 **/
@Component
public class ConversionConfigSampleType extends AbstractConfigDataStrategy {
    
    /**
     * 转换配置数据离线数据包（全量下载）
     *
     * @param onlineConfig 在线配置数据包
     * @return 配置数据列表
     */
    @Override
    public OfflineConfigPackageVO convertToOfflineConfig(OnlineConfigPackageVO onlineConfig) {
        return null;
    }
}
