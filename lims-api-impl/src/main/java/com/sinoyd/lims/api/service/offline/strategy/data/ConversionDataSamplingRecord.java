package com.sinoyd.lims.api.service.offline.strategy.data;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineCheckVO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 采样记录单数据转换策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 **/
@Component
public class ConversionDataSamplingRecord extends AbstractDataConversionStrategy {
    @Override
    public String getSupportedTableName() {
        return "";
    }

    @Override
    public OfflineDataPackageVO convertToOfflineData(OnlineDataPackageVO packageVO) {
        return null;
    }

    @Override
    public OnlineDataPackageVO convertToOnlineData(OfflineDataPackageVO packageVO) {
        return null;
    }

    @Override
    public List<OfflineCheckVO> check(OfflineDataPackageVO offlinePackage, OnlineDataPackageVO onlinePackage) {
        return Collections.emptyList();
    }
}
