package com.sinoyd.lims.api.service.offline.strategy.config;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineConfigPackageVO;
import org.springframework.stereotype.Component;

/**
 * 样品类型分组配置数据转换策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 **/
@Component
public class ConversionConfigSampleTypeGroup extends AbstractConfigDataStrategy {
    @Override
    public OfflineConfigPackageVO convertToOfflineConfig(OnlineConfigPackageVO onlineConfig) {
        return null;
    }
}
