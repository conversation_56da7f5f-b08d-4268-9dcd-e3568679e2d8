package com.sinoyd.lims.api.service.offline.strategy.data;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineCheckVO;
import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;

import java.util.List;

/**
 * 业务数据转换抽象基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public abstract class AbstractDataConversionStrategy implements IDataConversionStrategy {

    /**
     * 获取支持的表名
     *
     * @return 支持的表名
     */
    public abstract String getSupportedTableName();

    /**
     * 在线数据转换为离线数据（下载）
     *
     * @param packageVO 在线数据包
     * @return 离线数据列表
     */
    public abstract OfflineDataPackageVO convertToOfflineData(OnlineDataPackageVO packageVO);

    /**
     * 离线数据转换为在线数据（上传）
     *
     * @param packageVO 离线数据包
     */
    public abstract OnlineDataPackageVO convertToOnlineData(OfflineDataPackageVO packageVO);

    /**
     * 检测数据冲突
     *
     * @param offlinePackage 离线数据列表
     * @param onlinePackage  离线数据列表
     * @return 校验结果列表
     */
    public abstract List<OfflineCheckVO> check(OfflineDataPackageVO offlinePackage, OnlineDataPackageVO onlinePackage);
}
