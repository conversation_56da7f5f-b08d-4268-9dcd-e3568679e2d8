package com.sinoyd.lims.api.service.offline.factory;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineDataDownloadRequestVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineDataUploadRequestVO;
import com.sinoyd.lims.api.enums.EnumOfflineDataType;
import com.sinoyd.lims.api.service.offline.IConversionStrategyFactory;
import com.sinoyd.lims.api.service.offline.strategy.IConfigDataStrategy;
import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 转换策略工厂实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Component
public class ConversionStrategyFactoryImpl implements IConversionStrategyFactory {

    /**
     * 业务数据转换策略映射
     */
    private Map<String, IDataConversionStrategy> dataStrategies;

    /**
     * 配置数据转换策略映射
     */
    private Map<String, IConfigDataStrategy> configStrategies;


    @Override
    public List<OfflineDataPackageVO> convertOfflineData(OfflineDataDownloadRequestVO requestVO) {
        List<OfflineDataPackageVO> offlineDataList = new ArrayList<>();
        for (OnlineDataPackageVO onlineData : requestVO.getOnlineDataList()) {
            Arrays.stream(EnumOfflineDataType.values()).forEach(e ->
                    offlineDataList.add(getDataConversionStrategy(e.getTable()).convertToOfflineData(onlineData)));
        }
        return offlineDataList;
    }

    @Override
    public List<OfflineConfigPackageVO> convertOfflineConfig(OfflineDataDownloadRequestVO requestVO) {
        List<OfflineConfigPackageVO> offlineDataList = new ArrayList<>();
        for (OnlineConfigPackageVO onlineData : requestVO.getOnlineConfigList()) {
            Arrays.stream(EnumOfflineDataType.values()).forEach(e ->
                    offlineDataList.add(getConfigConversionStrategy(e.getTable()).convertToOfflineConfig(onlineData)));
        }
        return offlineDataList;
    }

    @Override
    public List<OnlineDataPackageVO> uploadBusinessData(OfflineDataUploadRequestVO requestVO) {
        List<OnlineDataPackageVO> onlineData = new ArrayList<>();
        for (OfflineDataPackageVO offlineData : requestVO.getOfflineDataList()) {
            Arrays.stream(EnumOfflineDataType.values()).forEach(e ->
                    onlineData.add(getDataConversionStrategy(e.getTable()).convertToOnlineData(offlineData)));
        }
        return onlineData;
    }

    /**
     * 获取业务数据转换策略
     *
     * @param tableName 表名
     * @return 业务数据转换策略
     */
    public IDataConversionStrategy getDataConversionStrategy(String tableName) {
        EnumOfflineDataType offlineDataType = EnumOfflineDataType.getEnumByTable(tableName);
        if (offlineDataType == null) {
            throw new BaseException("不支持的离线数据类型: " + tableName);
        }
        IDataConversionStrategy strategy = dataStrategies.get(offlineDataType.getBeanName());
        if (strategy == null) {
            throw new BaseException("未实现的离线业务数据转换策略: " + offlineDataType.getBeanName());
        }
        return strategy;
    }

    /**
     * 获取配置数据转换策略
     *
     * @param tableName 表名
     * @return 业务数据转换策略
     */
    public IConfigDataStrategy getConfigConversionStrategy(String tableName) {
        EnumOfflineDataType offlineDataType = EnumOfflineDataType.getEnumByTable(tableName);
        if (offlineDataType == null) {
            throw new BaseException("不支持的离线数据类型: " + tableName);
        }
        IConfigDataStrategy strategy = configStrategies.get(offlineDataType.getBeanName());
        if (strategy == null) {
            throw new BaseException("未实现的离线配置数据转换策略: " + offlineDataType.getBeanName());
        }
        return strategy;
    }

    @Autowired(required = false)
    public void setDataStrategies(Map<String, IDataConversionStrategy> dataStrategies) {
        this.dataStrategies = dataStrategies;
    }

    @Autowired(required = false)
    public void setConfigStrategies(Map<String, IConfigDataStrategy> configStrategies) {
        this.configStrategies = configStrategies;
    }
}
