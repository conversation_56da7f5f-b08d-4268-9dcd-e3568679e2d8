package com.sinoyd.lims.api.service.offline.strategy.data;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineCheckVO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 公共参数数据转换策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/24
 **/
@Component
public class ConversionDataPublicParamsData extends AbstractDataConversionStrategy {
    
    /**
     * 获取支持的表名
     *
     * @return 支持的表名
     */
    @Override
    public String getSupportedTableName() {
        return "public_params_data";
    }

    /**
     * 在线数据转换为离线数据（下载）
     *
     * @param packageVO 在线数据包
     * @return 离线数据包
     */
    @Override
    public OfflineDataPackageVO convertToOfflineData(OnlineDataPackageVO packageVO) {
        return null;
    }

    /**
     * 离线数据转换为在线数据（上传）
     *
     * @param packageVO 离线数据包
     * @return 在线数据包
     */
    @Override
    public OnlineDataPackageVO convertToOnlineData(OfflineDataPackageVO packageVO) {
        return null;
    }

    /**
     * 检测数据冲突
     *
     * @param offlinePackage 离线数据包
     * @param onlinePackage  在线数据包
     * @return 校验结果列表
     */
    @Override
    public List<OfflineCheckVO> check(OfflineDataPackageVO offlinePackage, OnlineDataPackageVO onlinePackage) {
        return Collections.emptyList();
    }
}
