package com.sinoyd.lims.api.service.offline;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineDataDownloadRequestVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineDataResponseVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineDataUploadRequestVO;
import com.sinoyd.lims.api.service.offline.strategy.IConfigDataStrategy;
import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;

import java.util.List;

/**
 * 转换策略工厂接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public interface IConversionStrategyFactory {

    /**
     * 下载业务数据包
     *
     * @param requestVO 下载请求参数
     * @return 业务离线数据响应
     */
    List<OfflineDataPackageVO> convertOfflineData(OfflineDataDownloadRequestVO requestVO);

    /**
     * 下载配置数据包
     *
     * @param requestVO 下载请求参数
     * @return 配置离线数据响应
     */
    List<OfflineConfigPackageVO> convertOfflineConfig(OfflineDataDownloadRequestVO requestVO);

    /**
     * 上传业务数据包
     *
     * @param requestVO 上传请求参数
     * @return 离线数据响应
     */
    List<OnlineDataPackageVO> uploadBusinessData(OfflineDataUploadRequestVO requestVO);
}
