package com.sinoyd.lims.api.dto.vo.offLine.data;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：点位签到表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineFolderSignVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 点位id
     */
    private String pointId;

    /**
     * 点位id
     */
    private String folderId;

    /**
     * 周期
     */
    private Integer periodCount;

    /**
     * 签到经度
     */
    private String signLon;

    /**
     * 签到纬度
     */
    private String signLat;

    /**
     * 签到说明
     */
    private String signTip;

    /**
     * 签到时间
     */
    private String signTime;

    /**
     * 签到人Id
     */
    private String signPersonId;

    /**
     * 签到人名称
     */
    private String signPersonName;

    /**
     * 所属机构
     */
    private String orgId;
}
