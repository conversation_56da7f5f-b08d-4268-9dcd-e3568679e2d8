package com.sinoyd.lims.api.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 离线数据录入离线数据库类型枚举
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/24
 */
@Getter
@AllArgsConstructor
public enum EnumOfflineDataType {

    //region 业务数据
    /**
     * 采样记录单
     */
    SAMPLE_RECORD("receive_sample_record", "conversionDataSamplingRecord"),

    /**
     * 点位
     */
    SAMPLE_FOLDER("sample_folder", "conversionDataSampleFolder"),

    /**
     * 点位签到
     */
    FOLDER_SIGN("folder_sign", "conversionDataFolderSign"),

    /**
     * 样品
     */
    SAMPLE_ITEM("sample_item", "conversionDataSampleItem"),

    /**
     * 分析数据
     */
    ANALYSE_DATA("analyse_data", "conversionDataAnalyseData"),

    /**
     * 样品分组
     */
    SAMPLE_GROUP("sample_group", "conversionDataSampleGroup"),

    /**
     * 公共参数
     */
    PUBLIC_PARAMS("public_params_data", "conversionDataPublicParamsData"),

    /**
     * 点位参数
     */
    FOLDER_PARAMS("folder_params_data", "conversionDataFolderParamsData"),

    /**
     * 样品参数
     */
    SAMPLE_PARAMS("sample_params_data", "conversionDataSampleParamsData"),

    /**
     * 附件
     */
    DOCUMENT("document", "conversionDataDocument"),
    //endregion

    //region 配置数据
    /**
     * 分组配置数据
     */
    SAMPLE_TYPE_GROUP("sample_type_group", "conversionConfigSampleTypeGroup"),

    /**
     * 分组测试项目关系
     */
    SAMPLE_TYPE_GROUP_TEST("sample_type_group_test", "conversionConfigSampleTypeGroupTest"),

    /**
     * 检测类型
     */
    SAMPLE_TYPE("sample_type", "conversionConfigSampleType"),

    /**
     * 人员
     */
    PERSON("person", "conversionConfigPerson");
    //endregion

    /**
     * 表名
     */
    private final String table;

    /**
     * 策略类名
     */
    private final String beanName;

    /**
     * 根据表名获取枚举
     *
     * @param table 表名
     * @return 枚举
     */
    public static EnumOfflineDataType getEnumByTable(String table) {
        for (EnumOfflineDataType c : EnumOfflineDataType.values()) {
            if (c.getTable().equals(table)) {
                return c;
            }
        }
        return null;
    }

}
