package com.sinoyd.lims.api.dto.vo.offLine.data;

import lombok.Data;

/**
 * 离线数据录入（离线数据表）：样品数据表实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 */
@Data
public class OffLineSampleParamsDataVO {

    /**
     * 主键ID，UUID格式
     */
    private String id;

    /**
     * 采样id
     */
    private String receiveId;

    /**
     * 点位ID，关联点位表（按周期拆分）
     */
    private String pointId;

    /**
     * 样品编号
     */
    private String code;

    /**
     * 样品类型id
     */
    private String sampleTypeId;

    /**
     * 点位名称
     */
    private String redFolderName;

    /**
     * QC类型
     */
    private String qcType;

    /**
     * 参数数据JSON字符串
     */
    private String paramBucket;

    /**
     * 所属机构
     */
    private String orgId;
}
