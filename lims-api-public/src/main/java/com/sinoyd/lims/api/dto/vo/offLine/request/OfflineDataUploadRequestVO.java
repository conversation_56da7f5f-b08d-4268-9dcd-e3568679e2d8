package com.sinoyd.lims.api.dto.vo.offLine.request;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 业务数据上传请求VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OfflineDataUploadRequestVO {

    /**
     * 所属机构编码
     */
    private String orgCode;

    /**
     * 所属机构
     */
    private String orgId;

    /**
     * 选择需要上传同步的采样单id集合
     */
    private Collection<String> receiveIds = new ArrayList<>();

    /**
     * 离线数据包数据
     */
    private List<OfflineDataPackageVO> offlineDataList = new ArrayList<>();
}
