package com.sinoyd.lims.api.dto.vo.offLine.request;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import lombok.Data;

import java.util.Collection;

/**
 * 离线数据录入响应VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OfflineDataResponseVO {
    
    /**
     * 所属机构编码
     */
    private String orgCode;
    
    /**
     * 所属机构
     */
    private String orgId;
    
    /**
     * 业务数据包集合
     */
    private Collection<OfflineDataPackageVO> dataPackages;
    
    /**
     * 配置数据包集合
     */
    private Collection<OfflineConfigPackageVO> configPackages;
}
